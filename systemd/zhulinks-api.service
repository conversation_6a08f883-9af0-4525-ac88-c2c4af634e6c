# ZhuLinks API Systemd 服务配置文件
# 位置: /etc/systemd/system/zhulinks-api.service
# 
# 这是一个替代 Supervisor 的 systemd 服务配置
# 如果您更喜欢使用 systemd 而不是 supervisor

[Unit]
Description=ZhuLinks API Service
Documentation=https://github.com/your-org/zhulinks-api
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/app
Environment=PYTHONPATH=/app
Environment=PYTHONUNBUFFERED=1
ExecStart=/app/venv/bin/gunicorn -c gunicorn.conf.py app.main:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=30
PrivateTmp=true
Restart=always
RestartSec=5
StartLimitInterval=60s
StartLimitBurst=3

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/app/logs /tmp
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=zhulinks-api

[Install]
WantedBy=multi-user.target
