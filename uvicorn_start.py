#!/usr/bin/env python3
"""
Uvicorn 直接启动脚本
用于开发环境或简单部署
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import uvicorn
from app.core.config import settings

if __name__ == "__main__":
    # Uvicorn 配置
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        log_level="info" if not settings.DEBUG else "debug",
        access_log=True,
        reload=settings.DEBUG,  # 只在开发环境启用热重载
        workers=1,  # Uvicorn 单进程模式
        use_colors=settings.DEBUG,  # 只在开发环境使用颜色
        server_header=False,  # 隐藏服务器头信息
        date_header=True,
    )
