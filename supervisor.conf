; ZhuLinks API 应用程序配置
[program:zhulinks-api]
command=/usr/local/bin/gunicorn -c gunicorn.conf.py app.main:app
directory=/app                          ; 工作目录（根据实际部署路径调整）
user=www-data                          ; 运行用户（根据实际情况调整）
group=www-data                         ; 运行组（根据实际情况调整）
autostart=true                         ; 自动启动
autorestart=true                       ; 自动重启
startretries=3                         ; 启动重试次数
redirect_stderr=true                   ; 重定向错误输出到标准输出
stdout_logfile=/var/log/supervisor/zhulinks-api.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/var/log/supervisor/zhulinks-api-error.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
environment=PYTHONPATH="/app",PYTHONUNBUFFERED="1"

; 优雅关闭配置
stopsignal=TERM                        ; 停止信号
stopwaitsecs=30                        ; 等待停止的时间
killasgroup=true                       ; 杀死整个进程组
stopasgroup=true                       ; 停止整个进程组

; 进程优先级
priority=999                           ; 启动优先级
