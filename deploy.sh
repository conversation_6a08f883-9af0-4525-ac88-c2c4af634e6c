#!/bin/bash

# ZhuLinks API 生产环境部署脚本
# 使用 Gunicorn + Supervisor 部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="zhulinks-api"
PROJECT_DIR="/app"
VENV_DIR="/app/venv"
USER="www-data"
GROUP="www-data"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        exit 1
    fi
}

# 安装系统依赖
install_system_dependencies() {
    log_step "安装系统依赖..."
    
    apt-get update
    apt-get install -y \
        python3 \
        python3-pip \
        python3-venv \
        supervisor \
        nginx \
        postgresql-client \
        redis-tools \
        curl \
        wget \
        git \
        build-essential \
        libpq-dev \
        python3-dev
    
    log_info "系统依赖安装完成"
}

# 创建项目用户和目录
setup_project_structure() {
    log_step "设置项目结构..."
    
    # 创建用户（如果不存在）
    if ! id "$USER" &>/dev/null; then
        useradd -r -s /bin/false -d "$PROJECT_DIR" "$USER"
        log_info "创建用户: $USER"
    fi
    
    # 创建项目目录
    mkdir -p "$PROJECT_DIR"
    mkdir -p "$PROJECT_DIR/logs"
    mkdir -p "/var/log/supervisor"
    
    # 设置权限
    chown -R "$USER:$GROUP" "$PROJECT_DIR"
    
    log_info "项目结构设置完成"
}

# 安装 Python 依赖
install_python_dependencies() {
    log_step "安装 Python 依赖..."
    
    # 创建虚拟环境
    if [ ! -d "$VENV_DIR" ]; then
        python3 -m venv "$VENV_DIR"
        log_info "创建虚拟环境: $VENV_DIR"
    fi
    
    # 激活虚拟环境并安装依赖
    source "$VENV_DIR/bin/activate"
    pip install --upgrade pip
    
    # 安装 gunicorn 和 uvicorn
    pip install gunicorn uvicorn[standard]
    
    # 如果存在 requirements.txt 或 pyproject.toml，安装项目依赖
    if [ -f "$PROJECT_DIR/requirements.txt" ]; then
        pip install -r "$PROJECT_DIR/requirements.txt"
    elif [ -f "$PROJECT_DIR/pyproject.toml" ]; then
        pip install -e .
    fi
    
    # 设置虚拟环境权限
    chown -R "$USER:$GROUP" "$VENV_DIR"
    
    log_info "Python 依赖安装完成"
}

# 配置 Supervisor
configure_supervisor() {
    log_step "配置 Supervisor..."
    
    # 复制配置文件
    cp "$PROJECT_DIR/supervisor.conf" "/etc/supervisor/conf.d/$PROJECT_NAME.conf"
    
    # 更新配置文件中的路径
    sed -i "s|/app|$PROJECT_DIR|g" "/etc/supervisor/conf.d/$PROJECT_NAME.conf"
    sed -i "s|/usr/local/bin/gunicorn|$VENV_DIR/bin/gunicorn|g" "/etc/supervisor/conf.d/$PROJECT_NAME.conf"
    
    # 重新加载 Supervisor 配置
    supervisorctl reread
    supervisorctl update
    
    log_info "Supervisor 配置完成"
}

# 配置 Nginx（可选）
configure_nginx() {
    log_step "配置 Nginx..."
    
    cat > "/etc/nginx/sites-available/$PROJECT_NAME" << EOF
server {
    listen 80;
    server_name _;  # 替换为实际域名
    
    client_max_body_size 100M;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    location /static/ {
        alias $PROJECT_DIR/statics/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
    
    # 启用站点
    ln -sf "/etc/nginx/sites-available/$PROJECT_NAME" "/etc/nginx/sites-enabled/"
    
    # 删除默认站点
    rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    nginx -t
    
    log_info "Nginx 配置完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    # 启动 Supervisor
    systemctl enable supervisor
    systemctl start supervisor
    
    # 启动应用
    supervisorctl start "$PROJECT_NAME"
    
    # 启动 Nginx
    systemctl enable nginx
    systemctl restart nginx
    
    log_info "服务启动完成"
}

# 检查服务状态
check_services() {
    log_step "检查服务状态..."
    
    echo "Supervisor 状态:"
    supervisorctl status
    
    echo -e "\nNginx 状态:"
    systemctl status nginx --no-pager -l
    
    echo -e "\n应用健康检查:"
    if curl -f http://localhost/health > /dev/null 2>&1; then
        log_info "应用运行正常"
    else
        log_warn "应用可能未正常启动，请检查日志"
    fi
}

# 显示有用信息
show_info() {
    log_step "部署完成信息:"
    
    echo -e "${GREEN}部署完成！${NC}"
    echo ""
    echo "项目目录: $PROJECT_DIR"
    echo "虚拟环境: $VENV_DIR"
    echo "日志目录: $PROJECT_DIR/logs"
    echo "Supervisor 日志: /var/log/supervisor/"
    echo ""
    echo "常用命令:"
    echo "  查看应用状态: supervisorctl status $PROJECT_NAME"
    echo "  重启应用: supervisorctl restart $PROJECT_NAME"
    echo "  查看应用日志: tail -f /var/log/supervisor/$PROJECT_NAME.log"
    echo "  查看错误日志: tail -f /var/log/supervisor/$PROJECT_NAME-error.log"
    echo "  重新加载配置: supervisorctl reread && supervisorctl update"
    echo ""
    echo "API 访问地址: http://your-server-ip/"
    echo "API 文档: http://your-server-ip/api/v1/docs"
}

# 主函数
main() {
    log_info "开始部署 $PROJECT_NAME..."
    
    check_root
    install_system_dependencies
    setup_project_structure
    install_python_dependencies
    configure_supervisor
    configure_nginx
    start_services
    check_services
    show_info
    
    log_info "部署脚本执行完成！"
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
