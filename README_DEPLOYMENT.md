# ZhuLinks API 生产环境部署指南

本文档提供了 ZhuLinks API 项目的完整生产环境部署方案，包括多种部署方式和配置选项。

## 部署方案概览

我们提供了以下几种部署方案：

1. **传统部署**: Gunicorn + Supervisor + Nginx
2. **Systemd 部署**: Gunicorn + Systemd + Nginx  
3. **Docker 部署**: Docker + Docker Compose
4. **自动化部署**: 使用部署脚本

## 方案一：传统部署 (Gunicorn + Supervisor)

### 1. 系统要求

- Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- Python 3.11+
- PostgreSQL 14+
- Redis 6+
- Nginx 1.18+

### 2. 快速部署

```bash
# 1. 克隆项目到服务器
git clone <your-repo-url> /app
cd /app

# 2. 运行自动部署脚本
chmod +x deploy.sh
sudo ./deploy.sh
```

### 3. 手动部署步骤

#### 3.1 安装系统依赖

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3 python3-pip python3-venv supervisor nginx postgresql-client redis-tools

# CentOS/RHEL
sudo yum install -y python3 python3-pip supervisor nginx postgresql redis
```

#### 3.2 创建项目环境

```bash
# 创建项目用户
sudo useradd -r -s /bin/false -d /app www-data

# 创建项目目录
sudo mkdir -p /app/logs
sudo chown -R www-data:www-data /app

# 创建虚拟环境
cd /app
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install gunicorn uvicorn[standard]
pip install -e .
```

#### 3.3 配置 Supervisor

```bash
# 复制配置文件
sudo cp supervisor.conf /etc/supervisor/conf.d/zhulinks-api.conf

# 更新配置中的路径
sudo sed -i 's|/app|/app|g' /etc/supervisor/conf.d/zhulinks-api.conf
sudo sed -i 's|/usr/local/bin/gunicorn|/app/venv/bin/gunicorn|g' /etc/supervisor/conf.d/zhulinks-api.conf

# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start zhulinks-api
```

#### 3.4 配置 Nginx

```bash
# 复制 Nginx 配置
sudo cp nginx.conf /etc/nginx/sites-available/zhulinks-api
sudo ln -sf /etc/nginx/sites-available/zhulinks-api /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

### 4. 常用管理命令

```bash
# 查看应用状态
sudo supervisorctl status zhulinks-api

# 重启应用
sudo supervisorctl restart zhulinks-api

# 查看日志
sudo tail -f /var/log/supervisor/zhulinks-api.log

# 重新加载配置
sudo supervisorctl reread && sudo supervisorctl update
```

## 方案二：Systemd 部署

### 1. 安装服务

```bash
# 复制服务文件
sudo cp systemd/zhulinks-api.service /etc/systemd/system/

# 重新加载 systemd
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable zhulinks-api
sudo systemctl start zhulinks-api
```

### 2. 管理命令

```bash
# 查看状态
sudo systemctl status zhulinks-api

# 重启服务
sudo systemctl restart zhulinks-api

# 查看日志
sudo journalctl -u zhulinks-api -f

# 停止服务
sudo systemctl stop zhulinks-api
```

## 方案三：Docker 部署

### 1. 构建镜像

```bash
# 构建应用镜像
docker build -f docker/Dockerfile -t zhulinks-api:latest .
```

### 2. 使用 Docker Compose

```bash
# 启动所有服务
cd docker
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f api

# 停止服务
docker-compose down
```

### 3. 单独运行容器

```bash
# 运行应用容器
docker run -d \
  --name zhulinks-api \
  -p 8000:8000 \
  -v /app/logs:/app/logs \
  -v /app/local_settings.py:/app/local_settings.py:ro \
  zhulinks-api:latest
```

## 配置文件说明

### gunicorn.conf.py

Gunicorn 配置文件，包含：
- 工作进程数量和类型
- 绑定地址和端口
- 日志配置
- 性能优化设置

### supervisor.conf

Supervisor 配置文件，包含：
- 进程管理配置
- 自动重启设置
- 日志轮转配置
- 环境变量设置

### nginx.conf

Nginx 配置文件，包含：
- 反向代理配置
- 静态文件服务
- 安全头设置
- 限流配置

## 环境配置

### 1. 生产环境配置

创建 `local_settings.py` 文件：

```python
# 生产环境配置
DEBUG = False
PROJECT_NAME = "ZhuLinks API"

# 数据库配置
DATABASES = {
    "default": {
        "engine": "tortoise.backends.asyncpg",
        "credentials": {
            "host": "your-db-host",
            "port": 5432,
            "user": "your-db-user",
            "password": "your-secure-password",
            "database": "zhulinks_cert",
            "minsize": 1,
            "maxsize": 20,
        },
    }
}

# Redis 配置
REDIS_HOST = "your-redis-host"
REDIS_PORT = "6379"
REDIS_PASSWORD = "your-redis-password"

# JWT 密钥（请使用强密码）
JWT_SECRET_KEY = "your-very-secure-jwt-secret-key"

# CORS 配置（生产环境请限制域名）
BACKEND_CORS_ORIGINS = ["https://your-frontend-domain.com"]
```

### 2. SSL/HTTPS 配置

如果需要 HTTPS，请：

1. 获取 SSL 证书（Let's Encrypt 推荐）
2. 修改 `nginx.conf` 中的 HTTPS 配置
3. 重启 Nginx

```bash
# 使用 Certbot 获取免费 SSL 证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 监控和日志

### 1. 日志位置

- 应用日志: `/app/logs/app.log`
- Gunicorn 日志: `/app/logs/gunicorn_*.log`
- Supervisor 日志: `/var/log/supervisor/zhulinks-api.log`
- Nginx 日志: `/var/log/nginx/zhulinks_api_*.log`

### 2. 健康检查

```bash
# 检查应用健康状态
curl http://localhost/health

# 检查 API 文档
curl http://localhost/docs
```

### 3. 性能监控

如果使用 Docker Compose，可以访问：
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000 (admin/admin)

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查日志: `sudo tail -f /var/log/supervisor/zhulinks-api.log`
   - 检查配置: 确保 `local_settings.py` 配置正确
   - 检查权限: 确保 www-data 用户有正确权限

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查防火墙设置

3. **Redis 连接失败**
   - 检查 Redis 服务状态
   - 验证连接参数和密码

4. **Nginx 502 错误**
   - 检查 Gunicorn 是否正常运行
   - 检查端口是否正确
   - 查看 Nginx 错误日志

### 性能优化建议

1. **数据库优化**
   - 配置连接池大小
   - 添加适当的索引
   - 定期维护数据库

2. **缓存优化**
   - 合理使用 Redis 缓存
   - 配置适当的缓存过期时间

3. **应用优化**
   - 调整 Gunicorn 工作进程数
   - 优化数据库查询
   - 使用异步处理

## 安全建议

1. **系统安全**
   - 定期更新系统和依赖
   - 配置防火墙
   - 使用非 root 用户运行应用

2. **应用安全**
   - 使用强 JWT 密钥
   - 限制 CORS 域名
   - 配置适当的限流规则

3. **网络安全**
   - 使用 HTTPS
   - 配置安全头
   - 限制不必要的端口访问

## 备份和恢复

### 数据库备份

```bash
# 备份数据库
pg_dump -h localhost -U postgres zhulinks_cert > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
psql -h localhost -U postgres zhulinks_cert < backup_file.sql
```

### 应用备份

```bash
# 备份应用代码和配置
tar -czf app_backup_$(date +%Y%m%d_%H%M%S).tar.gz /app
```

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 检查配置文件
3. 参考故障排除部分
4. 联系技术支持团队
