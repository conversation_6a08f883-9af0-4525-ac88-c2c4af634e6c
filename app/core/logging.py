import logging
import sys
import warnings
from pathlib import Path

from loguru import logger

from app.core.config import settings

# 获取日志根目录
LOG_DIR = settings.LOG_DIR

# 确保日志目录存在
LOG_DIR.mkdir(parents=True, exist_ok=True)

# 生产环境日志配置
LOG_PATH = str(LOG_DIR / "app.log")

# 生产环境日志轮转配置
if settings.DEBUG:
    # 开发环境配置
    LOG_RETENTION = "7 days"  # 开发环境保留7天
    LOG_ROTATION = "10 MB"    # 开发环境10MB轮转
    LOG_LEVEL = "DEBUG"
    LOG_COMPRESSION = None    # 开发环境不压缩
else:
    # 生产环境配置
    LOG_RETENTION = "30 days"  # 生产环境保留30天
    LOG_ROTATION = "50 MB"     # 生产环境50MB轮转
    LOG_LEVEL = "INFO"
    LOG_COMPRESSION = "gz"     # 生产环境使用gzip压缩

# 生产环境优化的日志格式
if settings.DEBUG:
    # 开发环境：详细格式，包含颜色
    CONSOLE_FORMAT = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>"
    FILE_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{line} | {message}"
else:
    # 生产环境：简洁格式，便于日志分析
    CONSOLE_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}"
    FILE_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {process} | {name}:{line} | {message}"

# 移除默认的sink
logger.remove()

# 添加控制台输出
logger.add(
    sys.stderr,
    format=CONSOLE_FORMAT,
    level=LOG_LEVEL,
    colorize=settings.DEBUG,  # 只在开发环境启用颜色
    backtrace=settings.DEBUG,  # 只在开发环境显示完整堆栈
    diagnose=settings.DEBUG,   # 只在开发环境显示变量值
)

# 添加文件日志输出 - 生产环境优化
try:
    # 生产环境禁用 enqueue 以避免多进程资源泄漏
    use_enqueue = settings.DEBUG  # 只在开发环境使用异步写入

    logger.add(
        LOG_PATH,
        format=FILE_FORMAT,
        level=LOG_LEVEL,
        rotation=LOG_ROTATION,
        retention=LOG_RETENTION,
        compression=LOG_COMPRESSION,
        enqueue=use_enqueue,  # 生产环境使用同步写入避免资源泄漏
        backtrace=settings.DEBUG,  # 只在开发环境显示完整堆栈
        diagnose=settings.DEBUG,   # 只在开发环境显示变量值
        serialize=False,  # 不序列化为JSON，保持可读性
        catch=True,  # 捕获日志记录过程中的异常
    )
except Exception as e:
    # 如果文件日志配置失败，至少保证控制台日志可用
    logger.error(f"文件日志配置失败: {e}")
    logger.warning("将仅使用控制台日志输出")

# 忽略特定的警告 - 生产环境优化
warnings.filterwarnings("ignore", category=ResourceWarning, module="multiprocessing.resource_tracker")
warnings.filterwarnings("ignore", category=UserWarning, module="uvicorn.lifespan.on")
warnings.filterwarnings("ignore", category=UserWarning, module="multiprocessing.resource_tracker")

# 设置多进程启动方法（解决信号量泄漏问题）
import multiprocessing
try:
    # 在 macOS 和某些 Linux 系统上使用 'spawn' 方法可以避免资源泄漏
    if multiprocessing.get_start_method(allow_none=True) is None:
        multiprocessing.set_start_method('spawn', force=True)
except RuntimeError:
    # 如果已经设置过启动方法，忽略错误
    pass


# 设置FastAPI的日志处理器
class InterceptHandler(logging.Handler):
    """拦截标准库日志并重定向到loguru"""

    def emit(self, record):
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


def setup():
    """配置日志系统，将标准库日志重定向到loguru"""
    try:
        # 设置日志级别
        log_level = logging.DEBUG if settings.DEBUG else logging.INFO

        # 配置根日志记录器
        logging.basicConfig(handlers=[InterceptHandler()], level=log_level, force=True)

        # 配置常用的第三方库日志
        framework_loggers = [
            "uvicorn",
            "uvicorn.error",
            "uvicorn.access",
            "fastapi",
        ]

        for logger_name in framework_loggers:
            framework_logger = logging.getLogger(logger_name)
            framework_logger.handlers = [InterceptHandler()]
            framework_logger.propagate = False

        # 生产环境优化：设置数据库相关日志级别
        db_loggers = [
            "tortoise", "asyncpg", "sqlalchemy", "alembic",
            "databases", "aiomysql", "aiopg", "pymongo", "motor"
        ]

        db_log_level = logging.WARNING if not settings.DEBUG else logging.INFO
        for db_logger in db_loggers:
            db_log = logging.getLogger(db_logger)
            db_log.setLevel(db_log_level)
            db_log.propagate = False

        # 生产环境优化：设置其他可能产生大量日志的库
        verbose_loggers = [
            "aioredis", "httpx", "urllib3", "requests",
            "elasticsearch", "boto3", "botocore", "asyncio"
        ]

        verbose_log_level = logging.ERROR if not settings.DEBUG else logging.WARNING
        for verbose_logger in verbose_loggers:
            verbose_log = logging.getLogger(verbose_logger)
            verbose_log.setLevel(verbose_log_level)
            verbose_log.propagate = False

        # 记录日志系统配置信息
        logger.info(f"日志系统初始化完成 - 环境: {'开发' if settings.DEBUG else '生产'}")
        logger.info(f"日志级别: {LOG_LEVEL}")
        logger.info(f"日志文件: {LOG_PATH}")
        logger.info(f"日志轮转: {LOG_ROTATION}")
        logger.info(f"日志保留: {LOG_RETENTION}")

        return logger

    except Exception as e:
        # 如果日志系统配置失败，使用基本配置
        print(f"日志系统配置失败: {e}")
        logging.basicConfig(level=logging.INFO)
        return logger


def get_logger():
    """获取全局logger实例"""
    return logger


def cleanup_logging():
    """
    清理日志系统资源
    在应用关闭时调用，确保所有日志资源正确释放
    """
    try:
        logger.info("开始清理日志系统资源...")

        # 停止所有日志处理器
        logger.stop()

        # 移除所有处理器
        logger.remove()

        # 强制刷新所有缓冲区
        import sys
        sys.stdout.flush()
        sys.stderr.flush()

        logger.info("日志系统资源清理完成")

    except Exception as e:
        print(f"清理日志系统时出错: {e}")


def force_cleanup_multiprocessing():
    """
    强制清理多进程资源
    解决信号量泄漏问题 - 彻底避免ChildProcessError版本
    """
    try:
        import gc
        import os

        # 检查是否在gunicorn环境中
        is_gunicorn = (
            os.environ.get('GUNICORN_WORKER') == 'true' or
            os.environ.get('SERVER_SOFTWARE', '').startswith('gunicorn') or
            'gunicorn' in os.environ.get('_', '') or
            'gunicorn' in str(os.environ.get('SUPERVISOR_PROCESS_NAME', ''))
        )

        if is_gunicorn:
            # 在gunicorn环境中，只执行基本的垃圾回收，完全避免多进程操作
            gc.collect()
            logger.debug("在gunicorn环境中，只执行垃圾回收，跳过多进程资源清理")
            return

        # 只在非gunicorn环境中执行多进程清理
        logger.debug("在非gunicorn环境中，执行完整的多进程资源清理")

        # 强制垃圾回收
        gc.collect()

        # 尝试清理多进程资源
        try:
            import multiprocessing
            if hasattr(multiprocessing, '_cleanup'):
                multiprocessing._cleanup()
        except Exception as e:
            logger.debug(f"多进程清理时出现异常: {e}")

        # 清理资源跟踪器
        try:
            from multiprocessing import resource_tracker
            if hasattr(resource_tracker, '_resource_tracker'):
                tracker = resource_tracker._resource_tracker
                if tracker is not None and hasattr(tracker, '_stop'):
                    tracker._stop()
        except Exception as e:
            logger.debug(f"资源跟踪器清理时出现异常: {e}")

    except Exception as e:
        # 完全忽略所有清理错误，避免影响应用关闭
        logger.debug(f"多进程资源清理时出现异常，已忽略: {e}")
