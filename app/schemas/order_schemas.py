from datetime import datetime

from pydantic import BaseModel, Field


class OrderInfoResponse(BaseModel):
    id: int = Field(..., description="唯一ID")
    waybill_code: str = Field(..., description="面单号")
    order_id: str = Field(..., description="订单号")
    outer_oi_id: str = Field(..., description="外部订单号")
    sku_id: str = Field(..., description="商品ID")
    quantity: int = Field(..., description="商品数量")
    bind_status: int = Field(..., description="绑定状态")
    create_time: datetime = Field(..., description="创建时间")
    send_date: datetime = Field(..., description="发货时间")
    order_time: datetime = Field(..., description="订单时间")
    pay_time: datetime = Field(..., description="支付时间")
    shop_id: str = Field(..., description="店铺ID")
    shop_name: str | None = Field(..., description="店铺名称")
    erp_order_id: str = Field(..., description="erp订单号")
    author_id: str = Field(..., description="达人ID")
    author_name: str | None = Field(..., description="达人名称")
    order_status: str = Field(..., description="订单状态")
    order_status_desc: str = Field(..., description="订单状态描述")
    spec_code: str = Field(..., description="规格编码")
    product_name: str = Field(..., description="商品名称")
    product_image: str = Field(..., description="商品图片")
