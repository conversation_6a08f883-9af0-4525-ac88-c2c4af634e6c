# Gunicorn 生产部署指南

## 问题解决

### "No child processes" 错误

如果您遇到 `清理多进程资源时出错: [Errno 10] No child processes` 错误，这是在gunicorn多进程环境下的常见问题。我们已经优化了配置来解决这个问题。

## 优化内容

### 1. Gunicorn 配置优化 (`gunicorn.conf.py`)

- 添加了专门的钩子函数来处理worker生命周期
- 优化了环境变量设置
- 改进了进程退出时的清理逻辑

### 2. 多进程资源清理优化 (`app/core/logging.py`)

- 检测gunicorn环境，避免在worker中执行复杂清理
- 将"No child processes"错误降级为调试信息
- 只在非gunicorn环境中执行完整的资源清理

### 3. 专用生产启动模块 (`production_start.py`)

- 专门为gunicorn设计的启动模块
- 避免复杂的信号处理和资源清理
- 让gunicorn自己处理进程管理

## 使用方法

### 方式一：直接启动
```bash
# 安装依赖
pip install gunicorn uvicorn[standard]

# 启动应用
./start.sh
```

### 方式二：手动启动
```bash
# 设置环境变量
export SERVER_SOFTWARE="gunicorn"

# 启动应用
gunicorn -c gunicorn.conf.py production_start:app
```

### 方式三：使用supervisor
```bash
# 修改supervisor.conf中的项目路径
# 然后复制配置文件
sudo cp supervisor.conf /etc/supervisor/conf.d/zhulinks-api.conf

# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动应用
sudo supervisorctl start zhulinks-api
```

## 配置说明

### Gunicorn 配置要点

- **工作进程数**: 自动计算 (CPU核心数 × 2 + 1)
- **Worker类型**: UvicornWorker (支持FastAPI异步)
- **预加载应用**: 提高性能和内存使用效率
- **优雅关闭**: 30秒超时，避免强制杀死进程

### 环境变量

- `SERVER_SOFTWARE=gunicorn`: 标识gunicorn环境
- `PYTHONUNBUFFERED=1`: 确保日志实时输出
- `PYTHONDONTWRITEBYTECODE=1`: 避免生成.pyc文件

## 常用管理命令

```bash
# 查看进程状态
ps aux | grep gunicorn

# 优雅重启 (发送HUP信号)
kill -HUP $(cat logs/gunicorn.pid)

# 优雅停止 (发送TERM信号)
kill -TERM $(cat logs/gunicorn.pid)

# 查看日志
tail -f logs/gunicorn_access.log
tail -f logs/gunicorn_error.log
```

## 性能调优

### 工作进程数调整

根据您的服务器配置调整 `gunicorn.conf.py` 中的 `workers` 参数：

```python
# CPU密集型应用
workers = multiprocessing.cpu_count() + 1

# I/O密集型应用 (默认)
workers = multiprocessing.cpu_count() * 2 + 1

# 高并发应用
workers = multiprocessing.cpu_count() * 4 + 1
```

### 内存优化

- 启用 `preload_app = True` 共享应用代码
- 设置 `max_requests` 定期重启worker避免内存泄漏
- 使用 `max_requests_jitter` 避免所有worker同时重启

## 故障排除

### 1. 应用无法启动

检查日志：
```bash
tail -f logs/gunicorn_error.log
```

常见问题：
- 端口被占用：修改 `bind` 配置
- 权限问题：检查文件和目录权限
- 依赖缺失：确保安装了所有依赖

### 2. 性能问题

- 调整worker数量
- 检查数据库连接池配置
- 监控内存和CPU使用率

### 3. 内存泄漏

- 设置合适的 `max_requests` 值
- 检查应用代码中的内存泄漏
- 监控worker内存使用情况

## 监控建议

### 1. 进程监控

```bash
# 监控gunicorn进程
watch -n 1 'ps aux | grep gunicorn'

# 监控内存使用
watch -n 1 'ps aux | grep gunicorn | awk "{sum+=\$6} END {print sum/1024\" MB\"}"'
```

### 2. 日志监控

- 设置日志轮转避免磁盘空间不足
- 监控错误日志中的异常
- 分析访问日志的性能指标

### 3. 健康检查

```bash
# 简单健康检查
curl -f http://localhost:8000/health || echo "Service is down"
```

这样配置后，您应该不会再遇到"No child processes"错误了。
