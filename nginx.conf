# ZhuLinks API Nginx 配置文件
# 适用于生产环境的高性能配置

upstream zhulinks_api {
    # Gunicorn 后端服务器
    server 127.0.0.1:8000 max_fails=3 fail_timeout=30s;
    # 如果有多个实例，可以添加更多服务器
    # server 127.0.0.1:8001 max_fails=3 fail_timeout=30s;
    
    # 负载均衡策略
    # least_conn;  # 最少连接数
    # ip_hash;     # IP 哈希
}

# 限制请求频率
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login_limit:10m rate=1r/s;

# 主服务器配置
server {
    listen 80;
    listen [::]:80;
    server_name _;  # 替换为实际域名，如 api.zhulinks.com
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 客户端配置
    client_max_body_size 100M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 日志配置
    access_log /var/log/nginx/zhulinks_api_access.log;
    error_log /var/log/nginx/zhulinks_api_error.log;
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # API 路由
    location /api/ {
        # 限制请求频率
        limit_req zone=api_limit burst=20 nodelay;
        
        proxy_pass http://zhulinks_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # 登录接口特殊限制
    location /api/v1/auth/login {
        limit_req zone=login_limit burst=5 nodelay;
        
        proxy_pass http://zhulinks_api;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 文档路由（可选择性开放）
    location /docs {
        # 可以添加 IP 白名单限制
        # allow ***********/24;
        # deny all;
        
        proxy_pass http://zhulinks_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件服务
    location /static/ {
        alias /app/statics/;
        expires 30d;
        add_header Cache-Control "public, immutable";
        
        # 安全配置
        location ~* \.(php|jsp|cgi|asp|aspx)$ {
            deny all;
        }
    }
    
    # 媒体文件服务
    location /media/ {
        alias /app/media/;
        expires 7d;
        add_header Cache-Control "public";
    }
    
    # 根路径重定向到 API 文档
    location = / {
        return 301 /docs;
    }
    
    # 隐藏 Nginx 版本
    server_tokens off;
    
    # 防止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 防止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# HTTPS 配置（使用 SSL 证书时启用）
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name api.zhulinks.com;
#     
#     # SSL 证书配置
#     ssl_certificate /path/to/certificate.crt;
#     ssl_certificate_key /path/to/private.key;
#     
#     # SSL 安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # 其他配置与 HTTP 相同...
# }
# 
# # HTTP 到 HTTPS 重定向
# server {
#     listen 80;
#     listen [::]:80;
#     server_name api.zhulinks.com;
#     return 301 https://$server_name$request_uri;
# }
