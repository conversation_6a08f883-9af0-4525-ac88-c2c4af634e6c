#!/usr/bin/env python3
"""
Gunicorn 生产环境配置文件 - 最简版本
专门解决 ChildProcessError: [Errno 10] No child processes 问题
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
LOG_DIR = PROJECT_ROOT / "logs"

# 确保日志目录存在
LOG_DIR.mkdir(parents=True, exist_ok=True)

# 服务器配置
bind = "0.0.0.0:8000"
backlog = 2048

# 工作进程配置 - 使用单进程避免多进程问题
workers = 1  # 单进程模式，彻底避免ChildProcessError
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 0  # 禁用worker重启，避免进程管理问题
timeout = 30
keepalive = 2

# 进程管理 - 最简配置
preload_app = False  # 禁用预加载
daemon = False
pidfile = str(LOG_DIR / "gunicorn.pid")

# 日志配置
accesslog = str(LOG_DIR / "gunicorn_access.log")
errorlog = str(LOG_DIR / "gunicorn_error.log")
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 环境变量
raw_env = [
    'PYTHONUNBUFFERED=1',
    'PYTHONDONTWRITEBYTECODE=1',
    'GUNICORN_WORKER=true',
]

# 最简钩子函数 - 避免任何可能导致子进程错误的操作
def on_starting(server):
    """服务器启动时的钩子"""
    server.log.info("Gunicorn 服务器正在启动...")
    os.environ['GUNICORN_WORKER'] = 'true'

def when_ready(server):
    """服务器准备就绪时的钩子"""
    server.log.info("Gunicorn 服务器已准备就绪")

def post_worker_init(worker):
    """工作进程初始化后的钩子"""
    worker.log.info(f"Worker {worker.pid} 初始化完成")
    os.environ['GUNICORN_WORKER'] = 'true'

def worker_exit(server, worker):
    """工作进程退出时的钩子 - 完全不执行任何操作"""
    # 什么都不做，避免ChildProcessError
    pass

def on_exit(server):
    """服务器退出时的钩子"""
    server.log.info("Gunicorn 服务器正在关闭...")
    os.environ.pop('GUNICORN_WORKER', None)
