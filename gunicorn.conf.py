#!/usr/bin/env python3
"""
Gunicorn 生产环境配置文件
适用于 ZhuLinks API 项目
"""

import multiprocessing
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
LOG_DIR = PROJECT_ROOT / "logs"

# 确保日志目录存在
LOG_DIR.mkdir(parents=True, exist_ok=True)

# 服务器配置
bind = "0.0.0.0:8000"
backlog = 2048

# 工作进程配置
workers = multiprocessing.cpu_count() * 2 + 1  # 推荐的工作进程数
worker_class = "uvicorn.workers.UvicornWorker"  # 使用 Uvicorn worker
worker_connections = 1000
max_requests = 1000  # 每个worker处理的最大请求数
max_requests_jitter = 50  # 随机抖动，避免所有worker同时重启
timeout = 30  # 工作进程超时时间
keepalive = 2  # Keep-Alive 连接的等待时间

# 进程管理
preload_app = True  # 预加载应用，提高性能
daemon = False  # 不以守护进程运行（由supervisor管理）
pidfile = str(LOG_DIR / "gunicorn.pid")
user = None  # 运行用户（由supervisor设置）
group = None  # 运行组（由supervisor设置）
tmp_upload_dir = None

# 日志配置
accesslog = str(LOG_DIR / "gunicorn_access.log")
errorlog = str(LOG_DIR / "gunicorn_error.log")
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# SSL配置（如果需要）
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"


# 钩子函数
def on_starting(server):
    """服务器启动时的钩子"""
    server.log.info("Gunicorn 服务器正在启动...")


def on_reload(server):
    """重载时的钩子"""
    server.log.info("Gunicorn 服务器正在重载...")


def when_ready(server):
    """服务器准备就绪时的钩子"""
    server.log.info("Gunicorn 服务器已准备就绪")


def worker_int(worker):
    """工作进程收到中断信号时的钩子"""
    worker.log.info("Worker 收到中断信号")


def pre_fork(server, worker):
    """fork 工作进程前的钩子"""
    server.log.info(f"Worker spawned (pid: {worker.pid})")


def post_fork(server, worker):
    """fork 工作进程后的钩子"""
    server.log.info(f"Worker spawned (pid: {worker.pid})")


def post_worker_init(worker):
    """工作进程初始化后的钩子"""
    worker.log.info(f"Worker initialized (pid: {worker.pid})")


def worker_abort(worker):
    """工作进程异常退出时的钩子"""
    worker.log.info(f"Worker aborted (pid: {worker.pid})")


def pre_exec(server):
    """执行前的钩子"""
    server.log.info("Forked child, re-executing.")


def pre_request(worker, req):
    """处理请求前的钩子"""
    worker.log.debug(f"{req.method} {req.path}")


def post_request(worker, req, environ, resp):
    """处理请求后的钩子"""
    pass


def child_exit(server, worker):
    """子进程退出时的钩子"""
    server.log.info(f"Worker exited (pid: {worker.pid})")


def worker_exit(server, worker):
    """工作进程退出时的钩子"""
    server.log.info(f"Worker exited (pid: {worker.pid})")


def nworkers_changed(server, new_value, old_value):
    """工作进程数量变化时的钩子"""
    server.log.info(f"Workers changed from {old_value} to {new_value}")


def on_exit(server):
    """服务器退出时的钩子"""
    server.log.info("Gunicorn 服务器正在关闭...")
