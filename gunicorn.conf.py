#!/usr/bin/env python3
"""
Gunicorn 生产环境配置文件 - 简化版
适用于 ZhuLinks API 项目
"""

import multiprocessing
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
LOG_DIR = PROJECT_ROOT / "logs"

# 确保日志目录存在
LOG_DIR.mkdir(parents=True, exist_ok=True)

# 服务器配置
bind = "0.0.0.0:8000"
backlog = 2048

# 工作进程配置
workers = multiprocessing.cpu_count() * 2 + 1  # 推荐的工作进程数
worker_class = "uvicorn.workers.UvicornWorker"  # 使用 Uvicorn worker
worker_connections = 1000
max_requests = 1000  # 每个worker处理的最大请求数
max_requests_jitter = 50  # 随机抖动，避免所有worker同时重启
timeout = 30  # 工作进程超时时间
keepalive = 2  # Keep-Alive 连接的等待时间

# 进程管理
preload_app = True  # 预加载应用，提高性能
daemon = False  # 不以守护进程运行
pidfile = str(LOG_DIR / "gunicorn.pid")

# 日志配置
accesslog = str(LOG_DIR / "gunicorn_access.log")
errorlog = str(LOG_DIR / "gunicorn_error.log")
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'
