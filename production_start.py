#!/usr/bin/env python3
"""
生产环境启动脚本 - 专为gunicorn优化
避免复杂的多进程资源清理，减少"No child processes"错误
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入应用
from app.main import app

# 这个文件专门用于gunicorn启动
# 不包含复杂的信号处理和资源清理逻辑
# 让gunicorn自己处理进程管理

if __name__ == "__main__":
    # 如果直接运行此文件，提示使用gunicorn
    print("此文件专为gunicorn设计，请使用以下命令启动:")
    print("gunicorn -c gunicorn.conf.py production_start:app")
    print("或者使用: ./start.sh")
