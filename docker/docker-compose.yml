version: '3.8'

services:
  # ZhuLinks API 应用
  api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: zhulinks-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - PYTHONUNBUFFERED=1
      - DEBUG=false
    volumes:
      - ../logs:/app/logs
      - ../local_settings.py:/app/local_settings.py:ro
    depends_on:
      - postgres
      - redis
    networks:
      - zhulinks-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: zhulinks-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ../statics:/app/statics:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - api
    networks:
      - zhulinks-network

  # PostgreSQL 数据库
  postgres:
    image: postgres:14-alpine
    container_name: zhulinks-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: zhulinks_cert
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: your_secure_password_here
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - zhulinks-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: zhulinks-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass your_redis_password_here
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - zhulinks-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 可选：监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: zhulinks-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - zhulinks-network

  # 可选：Grafana 仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: zhulinks-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - zhulinks-network

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  nginx-logs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  zhulinks-network:
    driver: bridge
